# .clang-format
# Merged configuration combining best practices for modern C++23 development
# Based on Google style with custom modifications for clarity and consistency
---
Language: Cpp
BasedOnStyle: Google

# Indentation and spacing - 4 spaces for better C++ readability
IndentWidth: 4
TabWidth: 4
UseTab: Never
AccessModifierOffset: -4

# Alignment settings - BlockIndent for better parameter formatting
AlignAfterOpenBracket: BlockIndent
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignEscapedNewlines: Left
AlignOperands: true
AlignTrailingComments: false

# Parameter and argument handling
AllowAllParametersOfDeclarationOnNextLine: false
BinPackArguments: false
BinPackParameters: false

# Short statement policies - strict for better readability
AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: All
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false

# Return type and template handling
AlwaysBreakAfterDefinitionReturnType: None
AlwaysBreakAfterReturnType: None
AlwaysBreakBeforeMultilineStrings: true
AlwaysBreakTemplateDeclarations: true

# Brace style - Allman for clear structure
BreakBeforeBraces: Allman
BraceWrapping:
    AfterClass: true
    AfterControlStatement: true
    AfterEnum: true
    AfterFunction: true
    AfterNamespace: false
    AfterStruct: true
    AfterUnion: true
    BeforeCatch: true
    BeforeElse: true
    IndentBraces: false
    SplitEmptyFunction: false
    SplitEmptyNamespace: true
    SplitEmptyRecord: true

# Line breaking and operators
BreakBeforeBinaryOperators: NonAssignment
BreakBeforeInheritanceComma: true
BreakBeforeTernaryOperators: true
BreakConstructorInitializers: BeforeColon
BreakStringLiterals: true

# Column limit - 100 for better code review experience
ColumnLimit: 100
# Comments and pragmas
CommentPragmas: "^ IWYU pragma:"
ReflowComments: true

# Namespace and class formatting
CompactNamespaces: false
NamespaceIndentation: Inner

# Constructor and continuation indentation
ConstructorInitializerAllOnOneLineOrOnePerLine: false
ConstructorInitializerIndentWidth: 4
ContinuationIndentWidth: 4

# Modern C++ settings
Cpp11BracedListStyle: true
Standard: C++23

# Pointer alignment - left for modern C++ style
DerivePointerAlignment: false
PointerAlignment: Left

# Include organization
FixNamespaceComments: true
SortIncludes: true
SortUsingDeclarations: true
IncludeCategories:
    - Priority: 1
      Regex: '^".*\.h"'
    - Priority: 2
      Regex: '^<.*\.h>'
    - Priority: 3
      Regex: "^<.*"
    - Priority: 4
      Regex: ".*"

# Case and indentation
IndentCaseLabels: false
IndentWrappedFunctionNames: true

# Empty lines and blocks
KeepEmptyLinesAtTheStartOfBlocks: true
MaxEmptyLinesToKeep: 2

# Spacing settings
SpaceAfterCStyleCast: true
SpaceAfterTemplateKeyword: false
SpaceBeforeAssignmentOperators: true
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles: false
SpacesInCStyleCastParentheses: false
SpacesInContainerLiterals: false
SpacesInParentheses: false
SpacesInSquareBrackets: false

# Penalties for line-breaking decisions (from previous config)
PenaltyBreakBeforeFirstCallParameter: 20
PenaltyBreakComment: 300
PenaltyBreakString: 1000
PenaltyExcessCharacter: 2
PenaltyReturnTypeOnItsOwnLine: 60

# Macro handling
ForEachMacros:
    - foreach
    - Q_FOREACH
    - BOOST_FOREACH
