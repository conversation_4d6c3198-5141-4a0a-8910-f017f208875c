# Template Usage Guide

This file provides a quick reference for customizing this C++23 project template.

## 🚀 Quick Setup Checklist

### 1. Rename the Project

-   [ ] Edit `CMakeLists.txt` and change `MyProject` to your project name
-   [ ] Update the `DESCRIPTION` field in `CMakeLists.txt`
-   [ ] Update the `VERSION` field in `CMakeLists.txt`

### 2. Customize Directory Structure

-   [ ] Rename `include/myproject/` to `include/yourproject/`
-   [ ] Update include paths in source files
-   [ ] Remove example files (`src/example.cpp`, `include/myproject/example.h`)

### 3. Update Documentation

-   [ ] Edit `README.md` with your project description
-   [ ] Update this file or delete it
-   [ ] Add your license file

### 4. Configure Git

-   [ ] Initialize git repository: `git init`
-   [ ] Add remote: `git remote add origin <your-repo-url>`
-   [ ] Make initial commit

## 📝 File Customization Guide

### CMakeLists.txt Changes

```cmake
# Change these lines:
project(
  YourProjectName              # <- Your project name
  VERSION 1.0.0               # <- Your version
  DESCRIPTION "Your project description"  # <- Your description
  LANGUAGES CXX)
```

### Source Files

1. Replace `src/main.cpp` with your application logic
2. Add new source files to `src/CMakeLists.txt`:
    ```cmake
    add_executable(${PROJECT_NAME}_app
        main.cpp
        your_file.cpp        # Add your files here
        another_file.cpp
    )
    ```

### Header Files

1. Create your header structure in `include/yourproject/`
2. Include headers using: `#include "yourproject/your_header.h"`

### Tests

1. Add test files to `test/`
2. Update `test/CMakeLists.txt` to include new test files
3. Use Catch2 macros: `TEST_CASE`, `SECTION`, `REQUIRE`

## 🔧 Build System Notes

### CMake Presets

-   `debug`: Development build with debug symbols
-   `release`: Optimized production build
-   `relwithdebinfo`: Optimized build with debug symbols

### Adding Dependencies

```cmake
# System libraries
find_package(YourLibrary REQUIRED)
target_link_libraries(${PROJECT_NAME}_app PRIVATE YourLibrary::YourLibrary)

# Header-only libraries via FetchContent
include(FetchContent)
FetchContent_Declare(
  YourLibrary
  GIT_REPOSITORY https://github.com/user/library.git
  GIT_TAG v1.0.0
)
FetchContent_MakeAvailable(YourLibrary)
target_link_libraries(${PROJECT_NAME}_app PRIVATE YourLibrary)
```

## 🎯 IDE Setup

### VS Code

1. Install clangd extension
2. Build project once to generate `compile_commands.json`
3. Restart language server if needed

### CLion

1. Open project folder
2. CLion auto-detects CMake configuration
3. Select build preset from dropdown

## 🧪 Testing Best Practices

### Test Organization

```cpp
TEST_CASE("Feature description", "[category][subcategory]")
{
    SECTION("Specific behavior")
    {
        // Setup
        auto object = create_test_object();

        // Action
        auto result = object.do_something();

        // Verify
        REQUIRE(result == expected_value);
    }
}
```

### Running Tests

```bash
# All tests
cmake --build build/debug --target test

# Specific categories
./build/debug/test/YourProject_tests "[category]"

# Verbose output
cd build/debug && ctest --verbose
```

## 📦 Deployment

### Release Build

```bash
cmake --preset release
cmake --build build/release
```

### Installation

```bash
# Add to CMakeLists.txt:
install(TARGETS ${PROJECT_NAME}_app DESTINATION bin)
install(DIRECTORY include/ DESTINATION include)

# Build and install:
cmake --build build/release --target install
```

## 🔄 Continuous Integration

### GitHub Actions Example

Create `.github/workflows/ci.yml`:

```yaml
name: CI
on: [push, pull_request]
jobs:
    build:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - name: Install dependencies
              run: sudo apt-get update && sudo apt-get install -y cmake ninja-build
            - name: Configure
              run: cmake --preset debug
            - name: Build
              run: cmake --build build/debug
            - name: Test
              run: cd build/debug && ctest --output-on-failure
```

## 🗑️ Cleanup

After customizing, you can safely delete:

-   [ ] `TEMPLATE_USAGE.md` (this file)
-   [ ] `src/example.cpp`
-   [ ] `include/myproject/example.h`
-   [ ] Example content from `src/main.cpp`
-   [ ] Example tests from `test/test_main.cpp`

## 📚 Additional Resources

-   [CMake Documentation](https://cmake.org/documentation/)
-   [Catch2 Tutorial](https://github.com/catchorg/Catch2/blob/devel/docs/tutorial.md)
-   [C++23 Reference](https://en.cppreference.com/w/cpp/23)
-   [Modern C++ Guidelines](https://isocpp.github.io/CppCoreGuidelines/)

---

**Happy coding with your new C++23 project! 🎉**
